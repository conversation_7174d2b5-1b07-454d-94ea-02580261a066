import { BaseScene } from '../core/SceneManager';
import { SCENES, GAME_CONFIG } from '../config/GameConfig';

/**
 * 登录场景
 * 处理用户登录、注册和游客模式
 */
export default class LoginScene extends BaseScene {
  constructor() {
    super();
    this.buttons = []; // 按钮列表
    this.inputFields = []; // 输入框列表
    this.currentInputIndex = -1; // 当前选中的输入框索引
    this.loginMode = 'login'; // 'login' | 'register' | 'guest'
    this.showPassword = false; // 是否显示密码
    this.errorMessage = ''; // 错误信息
    this.isLoading = false; // 是否正在加载
    
    // 用户输入数据
    this.userData = {
      username: '',
      password: '',
      confirmPassword: '' // 仅注册时使用
    };
    
    this.initUI();
  }

  /**
   * 初始化UI元素
   */
  initUI() {
    const centerX = GAME_CONFIG.CANVAS_WIDTH / 2;
    const inputWidth = 280;
    const inputHeight = 45;
    const buttonWidth = 200;
    const buttonHeight = 50;
    
    // 清空现有元素
    this.buttons = [];
    this.inputFields = [];
    
    // 根据当前模式初始化不同的UI
    if (this.loginMode === 'login') {
      this.initLoginUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight);
    } else if (this.loginMode === 'register') {
      this.initRegisterUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight);
    } else if (this.loginMode === 'guest') {
      this.initGuestUI(centerX, buttonWidth, buttonHeight);
    }
  }

  /**
   * 初始化登录界面
   */
  initLoginUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight) {
    // 用户名输入框
    this.inputFields.push({
      type: 'username',
      placeholder: '请输入用户名',
      x: centerX - inputWidth / 2,
      y: 280,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.username,
      focused: false
    });

    // 密码输入框
    this.inputFields.push({
      type: 'password',
      placeholder: '请输入密码',
      x: centerX - inputWidth / 2,
      y: 340,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.password,
      focused: false,
      isPassword: true
    });

    // 登录按钮
    this.buttons.push({
      text: '登录',
      x: centerX - buttonWidth / 2,
      y: 420,
      width: buttonWidth,
      height: buttonHeight,
      action: 'login',
      style: 'primary'
    });

    // 注册按钮
    this.buttons.push({
      text: '注册账号',
      x: centerX - buttonWidth / 2,
      y: 480,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToRegister',
      style: 'secondary'
    });

    // 游客登录按钮
    this.buttons.push({
      text: '游客模式',
      x: centerX - buttonWidth / 2,
      y: 540,
      width: buttonWidth,
      height: buttonHeight,
      action: 'guestLogin',
      style: 'tertiary'
    });

    // 显示/隐藏密码按钮
    this.buttons.push({
      text: this.showPassword ? '隐藏' : '显示',
      x: centerX + inputWidth / 2 - 50,
      y: 345,
      width: 40,
      height: 35,
      action: 'togglePassword',
      style: 'small'
    });
  }

  /**
   * 初始化注册界面
   */
  initRegisterUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight) {
    // 用户名输入框
    this.inputFields.push({
      type: 'username',
      placeholder: '请输入用户名(3-16位)',
      x: centerX - inputWidth / 2,
      y: 240,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.username,
      focused: false
    });

    // 密码输入框
    this.inputFields.push({
      type: 'password',
      placeholder: '请输入密码(6-20位)',
      x: centerX - inputWidth / 2,
      y: 300,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.password,
      focused: false,
      isPassword: true
    });

    // 确认密码输入框
    this.inputFields.push({
      type: 'confirmPassword',
      placeholder: '请再次输入密码',
      x: centerX - inputWidth / 2,
      y: 360,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.confirmPassword,
      focused: false,
      isPassword: true
    });

    // 注册按钮
    this.buttons.push({
      text: '注册',
      x: centerX - buttonWidth / 2,
      y: 440,
      width: buttonWidth,
      height: buttonHeight,
      action: 'register',
      style: 'primary'
    });

    // 返回登录按钮
    this.buttons.push({
      text: '返回登录',
      x: centerX - buttonWidth / 2,
      y: 500,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToLogin',
      style: 'secondary'
    });
  }

  /**
   * 初始化游客模式界面
   */
  initGuestUI(centerX, buttonWidth, buttonHeight) {
    // 确认游客登录按钮
    this.buttons.push({
      text: '确认游客登录',
      x: centerX - buttonWidth / 2,
      y: 350,
      width: buttonWidth,
      height: buttonHeight,
      action: 'confirmGuest',
      style: 'primary'
    });

    // 返回按钮
    this.buttons.push({
      text: '返回',
      x: centerX - buttonWidth / 2,
      y: 410,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToLogin',
      style: 'secondary'
    });
  }

  /**
   * 处理触摸事件
   */
  onTouch(event) {
    if (this.isLoading) return;

    const { x, y, type } = event;

    if (type === 'touchstart' || type === 'click') {
      // 检查输入框点击
      this.inputFields.forEach((field, index) => {
        if (this.isPointInRect(x, y, field)) {
          this.focusInput(index);
          return;
        }
      });

      // 检查按钮点击
      this.buttons.forEach(button => {
        if (this.isPointInRect(x, y, button)) {
          this.handleButtonClick(button.action);
          return;
        }
      });

      // 点击空白区域取消输入框焦点
      this.currentInputIndex = -1;
      this.inputFields.forEach(field => field.focused = false);
    }
  }

  /**
   * 聚焦输入框
   */
  focusInput(index) {
    this.currentInputIndex = index;
    this.inputFields.forEach((field, i) => {
      field.focused = i === index;
    });
    
    // 在微信小游戏中，这里可以调用键盘API
    // wx.showKeyboard({
    //   defaultValue: this.inputFields[index].value,
    //   maxLength: this.inputFields[index].type === 'username' ? 16 : 20,
    //   confirmType: 'done'
    // });
  }

  /**
   * 处理按钮点击
   */
  handleButtonClick(action) {
    switch (action) {
      case 'login':
        this.handleLogin();
        break;
      case 'register':
        this.handleRegister();
        break;
      case 'guestLogin':
        this.loginMode = 'guest';
        this.initUI();
        break;
      case 'confirmGuest':
        this.handleGuestLogin();
        break;
      case 'switchToRegister':
        this.loginMode = 'register';
        this.errorMessage = '';
        this.initUI();
        break;
      case 'switchToLogin':
        this.loginMode = 'login';
        this.errorMessage = '';
        this.initUI();
        break;
      case 'togglePassword':
        this.showPassword = !this.showPassword;
        this.initUI();
        break;
    }
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    if (!this.validateLoginInput()) return;

    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 这里应该调用实际的登录API
      // const result = await this.loginAPI(this.userData.username, this.userData.password);
      
      // 模拟登录过程
      await this.simulateLogin();
      
      // 登录成功，保存用户信息
      this.saveUserData({
        username: this.userData.username,
        isGuest: false,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);
      
    } catch (error) {
      this.errorMessage = error.message || '登录失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理注册
   */
  async handleRegister() {
    if (!this.validateRegisterInput()) return;

    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 这里应该调用实际的注册API
      // const result = await this.registerAPI(this.userData.username, this.userData.password);
      
      // 模拟注册过程
      await this.simulateRegister();
      
      // 注册成功，自动登录
      this.saveUserData({
        username: this.userData.username,
        isGuest: false,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);
      
    } catch (error) {
      this.errorMessage = error.message || '注册失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理游客登录
   */
  async handleGuestLogin() {
    this.isLoading = true;
    
    try {
      // 生成游客ID
      const guestId = 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // 保存游客信息
      this.saveUserData({
        username: guestId,
        isGuest: true,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);
      
    } catch (error) {
      this.errorMessage = '游客登录失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

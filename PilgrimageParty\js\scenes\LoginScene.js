import { BaseScene } from '../core/SceneManager';
import { SCENES, GAME_CONFIG } from '../config/GameConfig';

/**
 * 登录场景
 * 处理用户登录、注册和游客模式
 */
export default class LoginScene extends BaseScene {
  constructor() {
    super();
    this.buttons = []; // 按钮列表
    this.inputFields = []; // 输入框列表
    this.currentInputIndex = -1; // 当前选中的输入框索引
    this.loginMode = 'login'; // 'login' | 'register' | 'guest'
    this.showPassword = false; // 是否显示密码
    this.errorMessage = ''; // 错误信息
    this.isLoading = false; // 是否正在加载
    
    // 用户输入数据
    this.userData = {
      username: '',
      password: '',
      confirmPassword: '' // 仅注册时使用
    };
    
    this.initUI();
  }

  /**
   * 初始化UI元素
   */
  initUI() {
    const centerX = GAME_CONFIG.CANVAS_WIDTH / 2;
    const inputWidth = 280;
    const inputHeight = 45;
    const buttonWidth = 200;
    const buttonHeight = 50;
    
    // 清空现有元素
    this.buttons = [];
    this.inputFields = [];
    
    // 根据当前模式初始化不同的UI
    if (this.loginMode === 'login') {
      this.initLoginUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight);
    } else if (this.loginMode === 'register') {
      this.initRegisterUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight);
    } else if (this.loginMode === 'guest') {
      this.initGuestUI(centerX, buttonWidth, buttonHeight);
    }
  }

  /**
   * 初始化登录界面
   */
  initLoginUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight) {
    // 用户名输入框
    this.inputFields.push({
      type: 'username',
      placeholder: '请输入用户名',
      x: centerX - inputWidth / 2,
      y: 280,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.username,
      focused: false
    });

    // 密码输入框
    this.inputFields.push({
      type: 'password',
      placeholder: '请输入密码',
      x: centerX - inputWidth / 2,
      y: 340,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.password,
      focused: false,
      isPassword: true
    });

    // 登录按钮
    this.buttons.push({
      text: '登录',
      x: centerX - buttonWidth / 2,
      y: 420,
      width: buttonWidth,
      height: buttonHeight,
      action: 'login',
      style: 'primary'
    });

    // 注册按钮
    this.buttons.push({
      text: '注册账号',
      x: centerX - buttonWidth / 2,
      y: 480,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToRegister',
      style: 'secondary'
    });

    // 游客登录按钮
    this.buttons.push({
      text: '游客模式',
      x: centerX - buttonWidth / 2,
      y: 540,
      width: buttonWidth,
      height: buttonHeight,
      action: 'guestLogin',
      style: 'tertiary'
    });

    // 显示/隐藏密码按钮
    this.buttons.push({
      text: this.showPassword ? '隐藏' : '显示',
      x: centerX + inputWidth / 2 - 50,
      y: 345,
      width: 40,
      height: 35,
      action: 'togglePassword',
      style: 'small'
    });
  }

  /**
   * 初始化注册界面
   */
  initRegisterUI(centerX, inputWidth, inputHeight, buttonWidth, buttonHeight) {
    // 用户名输入框
    this.inputFields.push({
      type: 'username',
      placeholder: '请输入用户名(3-16位)',
      x: centerX - inputWidth / 2,
      y: 240,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.username,
      focused: false
    });

    // 密码输入框
    this.inputFields.push({
      type: 'password',
      placeholder: '请输入密码(6-20位)',
      x: centerX - inputWidth / 2,
      y: 300,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.password,
      focused: false,
      isPassword: true
    });

    // 确认密码输入框
    this.inputFields.push({
      type: 'confirmPassword',
      placeholder: '请再次输入密码',
      x: centerX - inputWidth / 2,
      y: 360,
      width: inputWidth,
      height: inputHeight,
      value: this.userData.confirmPassword,
      focused: false,
      isPassword: true
    });

    // 注册按钮
    this.buttons.push({
      text: '注册',
      x: centerX - buttonWidth / 2,
      y: 440,
      width: buttonWidth,
      height: buttonHeight,
      action: 'register',
      style: 'primary'
    });

    // 返回登录按钮
    this.buttons.push({
      text: '返回登录',
      x: centerX - buttonWidth / 2,
      y: 500,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToLogin',
      style: 'secondary'
    });
  }

  /**
   * 初始化游客模式界面
   */
  initGuestUI(centerX, buttonWidth, buttonHeight) {
    // 确认游客登录按钮
    this.buttons.push({
      text: '确认游客登录',
      x: centerX - buttonWidth / 2,
      y: 350,
      width: buttonWidth,
      height: buttonHeight,
      action: 'confirmGuest',
      style: 'primary'
    });

    // 返回按钮
    this.buttons.push({
      text: '返回',
      x: centerX - buttonWidth / 2,
      y: 410,
      width: buttonWidth,
      height: buttonHeight,
      action: 'switchToLogin',
      style: 'secondary'
    });
  }

  /**
   * 处理触摸事件
   */
  onTouch(event) {
    if (this.isLoading) return;

    const { x, y, type } = event;

    if (type === 'touchstart' || type === 'click') {
      // 检查输入框点击
      this.inputFields.forEach((field, index) => {
        if (this.isPointInRect(x, y, field)) {
          this.focusInput(index);
          return;
        }
      });

      // 检查按钮点击
      this.buttons.forEach(button => {
        if (this.isPointInRect(x, y, button)) {
          this.handleButtonClick(button.action);
          return;
        }
      });

      // 点击空白区域取消输入框焦点
      this.currentInputIndex = -1;
      this.inputFields.forEach(field => field.focused = false);
    }
  }

  /**
   * 聚焦输入框
   */
  focusInput(index) {
    this.currentInputIndex = index;
    this.inputFields.forEach((field, i) => {
      field.focused = i === index;
    });
    
    // 在微信小游戏中，这里可以调用键盘API
    // wx.showKeyboard({
    //   defaultValue: this.inputFields[index].value,
    //   maxLength: this.inputFields[index].type === 'username' ? 16 : 20,
    //   confirmType: 'done'
    // });
  }

  /**
   * 处理按钮点击
   */
  handleButtonClick(action) {
    switch (action) {
      case 'login':
        this.handleLogin();
        break;
      case 'register':
        this.handleRegister();
        break;
      case 'guestLogin':
        this.loginMode = 'guest';
        this.initUI();
        break;
      case 'confirmGuest':
        this.handleGuestLogin();
        break;
      case 'switchToRegister':
        this.loginMode = 'register';
        this.errorMessage = '';
        this.initUI();
        break;
      case 'switchToLogin':
        this.loginMode = 'login';
        this.errorMessage = '';
        this.initUI();
        break;
      case 'togglePassword':
        this.showPassword = !this.showPassword;
        this.initUI();
        break;
    }
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    if (!this.validateLoginInput()) return;

    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 这里应该调用实际的登录API
      // const result = await this.loginAPI(this.userData.username, this.userData.password);
      
      // 模拟登录过程
      await this.simulateLogin();
      
      // 登录成功，保存用户信息
      this.saveUserData({
        username: this.userData.username,
        isGuest: false,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);
      
    } catch (error) {
      this.errorMessage = error.message || '登录失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理注册
   */
  async handleRegister() {
    if (!this.validateRegisterInput()) return;

    this.isLoading = true;
    this.errorMessage = '';

    try {
      // 这里应该调用实际的注册API
      // const result = await this.registerAPI(this.userData.username, this.userData.password);
      
      // 模拟注册过程
      await this.simulateRegister();
      
      // 注册成功，自动登录
      this.saveUserData({
        username: this.userData.username,
        isGuest: false,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);
      
    } catch (error) {
      this.errorMessage = error.message || '注册失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 处理游客登录
   */
  async handleGuestLogin() {
    this.isLoading = true;

    try {
      // 生成游客ID
      const guestId = 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

      // 保存游客信息
      this.saveUserData({
        username: guestId,
        isGuest: true,
        loginTime: Date.now()
      });

      // 切换到主菜单
      this.sceneManager.switchTo(SCENES.MENU);

    } catch (error) {
      this.errorMessage = '游客登录失败，请重试';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 验证登录输入
   */
  validateLoginInput() {
    if (!this.userData.username.trim()) {
      this.errorMessage = '请输入用户名';
      return false;
    }

    if (!this.userData.password.trim()) {
      this.errorMessage = '请输入密码';
      return false;
    }

    return true;
  }

  /**
   * 验证注册输入
   */
  validateRegisterInput() {
    const username = this.userData.username.trim();
    const password = this.userData.password.trim();
    const confirmPassword = this.userData.confirmPassword.trim();

    if (!username) {
      this.errorMessage = '请输入用户名';
      return false;
    }

    if (username.length < 3 || username.length > 16) {
      this.errorMessage = '用户名长度应为3-16位';
      return false;
    }

    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
      this.errorMessage = '用户名只能包含字母、数字、下划线和中文';
      return false;
    }

    if (!password) {
      this.errorMessage = '请输入密码';
      return false;
    }

    if (password.length < 6 || password.length > 20) {
      this.errorMessage = '密码长度应为6-20位';
      return false;
    }

    if (password !== confirmPassword) {
      this.errorMessage = '两次输入的密码不一致';
      return false;
    }

    return true;
  }

  /**
   * 模拟登录过程
   */
  async simulateLogin() {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟登录验证
        const storedUsers = this.getStoredUsers();
        const user = storedUsers.find(u => u.username === this.userData.username);

        if (!user) {
          reject(new Error('用户不存在'));
          return;
        }

        if (user.password !== this.userData.password) {
          reject(new Error('密码错误'));
          return;
        }

        resolve({ success: true });
      }, 1000); // 模拟网络延迟
    });
  }

  /**
   * 模拟注册过程
   */
  async simulateRegister() {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟注册验证
        const storedUsers = this.getStoredUsers();
        const existingUser = storedUsers.find(u => u.username === this.userData.username);

        if (existingUser) {
          reject(new Error('用户名已存在'));
          return;
        }

        // 保存新用户
        storedUsers.push({
          username: this.userData.username,
          password: this.userData.password,
          registerTime: Date.now()
        });

        this.saveStoredUsers(storedUsers);
        resolve({ success: true });
      }, 1000); // 模拟网络延迟
    });
  }

  /**
   * 获取存储的用户数据
   */
  getStoredUsers() {
    try {
      const data = wx.getStorageSync('registered_users');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存用户数据到存储
   */
  saveStoredUsers(users) {
    try {
      wx.setStorageSync('registered_users', JSON.stringify(users));
    } catch (error) {
      console.error('保存用户数据失败:', error);
    }
  }

  /**
   * 保存当前登录用户信息
   */
  saveUserData(userData) {
    try {
      wx.setStorageSync('current_user', JSON.stringify(userData));
      GameGlobal.databus.currentUser = userData;
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  /**
   * 检查点是否在矩形内
   */
  isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
  }

  /**
   * 更新输入框内容
   */
  updateInputValue(type, value) {
    this.userData[type] = value;
    const field = this.inputFields.find(f => f.type === type);
    if (field) {
      field.value = value;
    }
  }

  /**
   * 场景更新
   */
  update(deltaTime) {
    // 这里可以添加动画更新逻辑
    // 例如按钮悬停效果、输入框光标闪烁等
  }

  /**
   * 场景渲染
   */
  render(ctx) {
    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);

    // 绘制背景装饰
    this.renderBackground(ctx);

    // 绘制标题
    this.renderTitle(ctx);

    // 绘制输入框
    this.renderInputFields(ctx);

    // 绘制按钮
    this.renderButtons(ctx);

    // 绘制错误信息
    this.renderErrorMessage(ctx);

    // 绘制加载状态
    if (this.isLoading) {
      this.renderLoading(ctx);
    }
  }

  /**
   * 渲染背景
   */
  renderBackground(ctx) {
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, GAME_CONFIG.CANVAS_HEIGHT);
    gradient.addColorStop(0, '#1a1a2e');
    gradient.addColorStop(0.5, '#16213e');
    gradient.addColorStop(1, '#0f3460');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);

    // 绘制装饰性元素
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * GAME_CONFIG.CANVAS_WIDTH;
      const y = Math.random() * GAME_CONFIG.CANVAS_HEIGHT;
      const radius = Math.random() * 3 + 1;

      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  /**
   * 渲染标题
   */
  renderTitle(ctx) {
    const centerX = GAME_CONFIG.CANVAS_WIDTH / 2;

    // 游戏标题
    ctx.fillStyle = '#ffd700';
    ctx.font = 'bold 32px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('一起来取经', centerX, 120);

    // 副标题
    let subtitle = '';
    if (this.loginMode === 'login') {
      subtitle = '欢迎回来，取经人！';
    } else if (this.loginMode === 'register') {
      subtitle = '加入取经之旅';
    } else if (this.loginMode === 'guest') {
      subtitle = '游客模式';
    }

    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial, sans-serif';
    ctx.fillText(subtitle, centerX, 160);

    // 模式说明
    if (this.loginMode === 'guest') {
      ctx.fillStyle = '#ffcc00';
      ctx.font = '14px Arial, sans-serif';
      ctx.fillText('游客模式下游戏进度不会保存', centerX, 280);
      ctx.fillText('建议注册账号以保存游戏数据', centerX, 300);
    }
  }

  /**
   * 渲染输入框
   */
  renderInputFields(ctx) {
    this.inputFields.forEach(field => {
      // 绘制输入框背景
      ctx.fillStyle = field.focused ? '#2a2a4e' : '#1e1e3e';
      ctx.fillRect(field.x, field.y, field.width, field.height);

      // 绘制边框
      ctx.strokeStyle = field.focused ? '#4a9eff' : '#444466';
      ctx.lineWidth = 2;
      ctx.strokeRect(field.x, field.y, field.width, field.height);

      // 绘制文本
      ctx.fillStyle = '#ffffff';
      ctx.font = '16px Arial, sans-serif';
      ctx.textAlign = 'left';

      const text = field.value || field.placeholder;
      const displayText = field.isPassword && field.value && !this.showPassword
        ? '*'.repeat(field.value.length)
        : text;

      const textColor = field.value ? '#ffffff' : '#888888';
      ctx.fillStyle = textColor;

      ctx.fillText(displayText, field.x + 15, field.y + field.height / 2 + 6);

      // 绘制光标
      if (field.focused && field.value) {
        const textWidth = ctx.measureText(displayText).width;
        ctx.fillStyle = '#4a9eff';
        ctx.fillRect(field.x + 15 + textWidth + 2, field.y + 10, 2, field.height - 20);
      }
    });
  }

  /**
   * 渲染按钮
   */
  renderButtons(ctx) {
    this.buttons.forEach(button => {
      // 根据按钮样式设置颜色
      let bgColor, textColor, borderColor;

      switch (button.style) {
        case 'primary':
          bgColor = this.isLoading ? '#666666' : '#4a9eff';
          textColor = '#ffffff';
          borderColor = '#4a9eff';
          break;
        case 'secondary':
          bgColor = 'transparent';
          textColor = '#4a9eff';
          borderColor = '#4a9eff';
          break;
        case 'tertiary':
          bgColor = 'transparent';
          textColor = '#ffcc00';
          borderColor = '#ffcc00';
          break;
        case 'small':
          bgColor = 'rgba(74, 158, 255, 0.2)';
          textColor = '#4a9eff';
          borderColor = '#4a9eff';
          break;
        default:
          bgColor = '#333333';
          textColor = '#ffffff';
          borderColor = '#666666';
      }

      // 绘制按钮背景
      ctx.fillStyle = bgColor;
      ctx.fillRect(button.x, button.y, button.width, button.height);

      // 绘制按钮边框
      ctx.strokeStyle = borderColor;
      ctx.lineWidth = 2;
      ctx.strokeRect(button.x, button.y, button.width, button.height);

      // 绘制按钮文本
      ctx.fillStyle = textColor;
      ctx.font = button.style === 'small' ? '12px Arial, sans-serif' : '16px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(
        button.text,
        button.x + button.width / 2,
        button.y + button.height / 2 + 6
      );
    });
  }

  /**
   * 渲染错误信息
   */
  renderErrorMessage(ctx) {
    if (this.errorMessage) {
      const centerX = GAME_CONFIG.CANVAS_WIDTH / 2;
      const y = this.loginMode === 'register' ? 420 : 400;

      ctx.fillStyle = '#ff4444';
      ctx.font = '14px Arial, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(this.errorMessage, centerX, y);
    }
  }

  /**
   * 渲染加载状态
   */
  renderLoading(ctx) {
    // 绘制半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);

    // 绘制加载动画
    const centerX = GAME_CONFIG.CANVAS_WIDTH / 2;
    const centerY = GAME_CONFIG.CANVAS_HEIGHT / 2;
    const time = Date.now() * 0.005;

    ctx.strokeStyle = '#4a9eff';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.arc(centerX, centerY, 30, time, time + Math.PI * 1.5);
    ctx.stroke();

    // 绘制加载文本
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText('加载中...', centerX, centerY + 60);
  }

  /**
   * 场景进入时调用
   */
  onEnter() {
    // 检查是否已有登录用户
    try {
      const userData = wx.getStorageSync('current_user');
      if (userData) {
        const user = JSON.parse(userData);
        // 如果用户数据有效且不是游客，直接跳转到主菜单
        if (user && !user.isGuest && Date.now() - user.loginTime < 7 * 24 * 60 * 60 * 1000) {
          GameGlobal.databus.currentUser = user;
          this.sceneManager.switchTo(SCENES.MENU);
          return;
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }

    // 重置状态
    this.errorMessage = '';
    this.isLoading = false;
    this.loginMode = 'login';
    this.initUI();
  }

  /**
   * 场景退出时调用
   */
  onExit() {
    // 清理资源
    this.currentInputIndex = -1;
    this.inputFields.forEach(field => field.focused = false);
  }

  /**
   * 销毁场景
   */
  destroy() {
    // 清理所有资源
    this.buttons = [];
    this.inputFields = [];
    this.userData = null;
  }
}

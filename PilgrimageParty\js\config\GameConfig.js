/**
 * 游戏配置文件
 * 包含所有游戏常量、配置参数和数据
 */

// 游戏基础配置
export const GAME_CONFIG = {
  // 画布尺寸
  CANVAS_WIDTH: 375,
  CANVAS_HEIGHT: 667,
  
  // 帧率
  TARGET_FPS: 60,
  
  // 游戏版本
  VERSION: '1.0.0'
};

// 角色配置
export const CHARACTERS = {
  WUKONG: {
    id: 'wukong',
    name: '孙悟空',
    weapon: '如意金箍棒',
    baseStats: {
      hp: 120,
      attack: 25,
      defense: 15,
      speed: 20,
      critRate: 0.15,
      critDamage: 1.5
    },
    skills: ['七十二变', '筋斗云', '火眼金睛', '大闹天宫']
  },
  BAJIE: {
    id: 'bajie',
    name: '猪八戒',
    weapon: '九齿钉耙',
    baseStats: {
      hp: 150,
      attack: 20,
      defense: 20,
      speed: 12,
      critRate: 0.08,
      critDamage: 1.3
    },
    skills: ['天蓬元帅', '三十六变', '倒打一耙', '猪突猛进']
  },
  WUJING: {
    id: 'wujing',
    name: '沙僧',
    weapon: '降妖宝杖',
    baseStats: {
      hp: 140,
      attack: 18,
      defense: 25,
      speed: 15,
      critRate: 0.05,
      critDamage: 1.2
    },
    skills: ['流沙河', '卷帘大将', '降妖除魔', '坚如磐石']
  },
  TANGSENG: {
    id: 'tangseng',
    name: '唐僧',
    weapon: '九环锡杖',
    baseStats: {
      hp: 100,
      attack: 15,
      defense: 10,
      speed: 18,
      critRate: 0.02,
      critDamage: 1.1
    },
    skills: ['紧箍咒', '佛光普照', '慈悲为怀', '诵经念佛']
  }
};

// 关卡配置
export const CHAPTERS = {
  1: {
    name: '金蝉初劫',
    levels: [
      { id: 1, name: '金蝉谪凡尘', difficulty: 1 },
      { id: 2, name: '满月抛江劫', difficulty: 1 },
      { id: 3, name: '双叉岭虎患', difficulty: 2 },
      { id: 4, name: '五行困心猿', difficulty: 2 },
      { id: 5, name: '鹰愁涧龙怒', difficulty: 3 },
      { id: 6, name: '禅院火窃袈', difficulty: 3 },
      { id: 7, name: '黄风迷魂阵', difficulty: 4 },
      { id: 8, name: '流沙河妖阻', difficulty: 4 },
      { id: 9, name: '四圣试禅心', difficulty: 5 }
    ]
  },
  2: {
    name: '情欲迷障',
    levels: [
      { id: 10, name: '白骨精诱惑', difficulty: 5 },
      { id: 11, name: '黄袍怪变身', difficulty: 6 },
      { id: 12, name: '银角大王', difficulty: 6 },
      { id: 13, name: '金角大王', difficulty: 7 },
      { id: 14, name: '红孩儿三昧真火', difficulty: 7 },
      { id: 15, name: '车迟国斗法', difficulty: 8 },
      { id: 16, name: '通天河鱼怪', difficulty: 8 },
      { id: 17, name: '女儿国诱惑', difficulty: 9 },
      { id: 18, name: '蝎子精毒针', difficulty: 9 }
    ]
  }
  // 其他章节配置...
};

// 装备配置
export const EQUIPMENT = {
  TYPES: {
    HELMET: 'helmet',
    ARMOR: 'armor',
    LEGGINGS: 'leggings',
    BOOTS: 'boots'
  },
  RARITIES: {
    COMMON: { name: '普通', color: '#ffffff', dropRate: 0.6 },
    RARE: { name: '稀有', color: '#00ff00', dropRate: 0.25 },
    EPIC: { name: '史诗', color: '#9932cc', dropRate: 0.1 },
    LEGENDARY: { name: '传说', color: '#ffa500', dropRate: 0.04 },
    MYTHIC: { name: '传奇', color: '#ff0000', dropRate: 0.01 }
  }
};

// 宝石配置
export const GEMS = {
  TYPES: {
    ATTACK: 'attack',
    DEFENSE: 'defense',
    HP: 'hp',
    CRIT_RATE: 'critRate',
    CRIT_DAMAGE: 'critDamage',
    SPEED: 'speed'
  },
  GRADES: {
    GREEN: { name: '绿色', color: '#00ff00', level: 1 },
    BLUE: { name: '蓝色', color: '#0080ff', level: 2 },
    PURPLE: { name: '紫色', color: '#8000ff', level: 3 },
    ORANGE: { name: '橙色', color: '#ff8000', level: 4 },
    LEGENDARY: { name: '传说', color: '#ff0000', level: 5 }
  },
  COMBINE_RATIO: 3 // 3个低级宝石合成1个高级宝石
};

// 精炼配置
export const REFINEMENT = {
  MAX_STARS: 5,
  COSTS: [5, 10, 20, 40, 50], // 每颗星的钻石消耗
  QUALITIES: {
    GRAY: { name: '灰色', color: '#808080', multiplier: 1.0 },
    BRONZE: { name: '铜色', color: '#cd7f32', multiplier: 1.2 },
    SILVER: { name: '银色', color: '#c0c0c0', multiplier: 1.5 },
    GOLD: { name: '金色', color: '#ffd700', multiplier: 2.0 }
  }
};

// 技能刷新配置
export const SKILL_REFRESH = {
  COSTS: [20, 50, 100], // 第1、2、3次刷新的钻石消耗
  MAX_REFRESHES: 20, // 每关最多刷新次数
  MAX_SKILLS: 21, // 每关最多解锁技能数
  SKILLS_PER_BATTLE: 4 // 每次战斗选择的技能数
};

// 货币配置
export const CURRENCY = {
  DIAMOND_RATE: 100, // 1元 = 100钻石
  DAILY_LOGIN: 50, // 每日登录奖励
  DAILY_TASKS: 100, // 每日任务奖励
  TRADE_FEE_RATE: 0.01, // 交易手续费率 1%
  MIN_TRADE_FEE: 1, // 最低交易手续费
  MIN_TRADE_PRICE: 50 // 最低上架价格
};

// 多人游戏配置
export const MULTIPLAYER = {
  MIN_PLAYERS: 2,
  MAX_PLAYERS: 4,
  MATCH_TIMEOUT: 30000, // 匹配超时时间 30秒
  BATTLE_TIMEOUT: 300000 // 战斗超时时间 5分钟
};

// 场景名称常量
export const SCENES = {
  LOGIN: 'login',
  MENU: 'menu',
  CHARACTER_SELECT: 'character_select',
  LEVEL_SELECT: 'level_select',
  BATTLE: 'battle',
  INVENTORY: 'inventory',
  SHOP: 'shop',
  MULTIPLAYER_LOBBY: 'multiplayer_lobby'
};

// 音效配置
export const AUDIO = {
  BGM: {
    MENU: 'audio/bgm_menu.mp3',
    BATTLE: 'audio/bgm_battle.mp3',
    VICTORY: 'audio/bgm_victory.mp3'
  },
  SFX: {
    CLICK: 'audio/sfx_click.mp3',
    ATTACK: 'audio/sfx_attack.mp3',
    SKILL: 'audio/sfx_skill.mp3',
    VICTORY: 'audio/sfx_victory.mp3',
    DEFEAT: 'audio/sfx_defeat.mp3'
  }
};
